# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.vercel

.vercel

.vercel
.env*.local

# Flutter/Mobile App
mobile/android/app/google-services.json
mobile/ios/Runner/GoogleService-Info.plist
mobile/android/key.properties
mobile/android/app/upload-keystore.jks
mobile/android/app/release-keystore.jks
mobile/.flutter-plugins
mobile/.flutter-plugins-dependencies
mobile/.dart_tool/
mobile/build/
mobile/ios/Pods/
mobile/ios/.symlinks/
mobile/ios/Flutter/Flutter.framework
mobile/ios/Flutter/Flutter.podspec
